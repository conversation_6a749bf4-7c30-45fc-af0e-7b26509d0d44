package com.huntergame.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;

import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import com.huntergame.manager.PlayerRoleManager;
import com.huntergame.HunterGameMod;

public class EscaperCommand {
    
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher) {
        dispatcher.register(CommandManager.literal("escaper")
            .executes(EscaperCommand::execute));
    }
    
    private static int execute(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        ServerCommandSource source = context.getSource();
        
        // Check if command is executed by a player
        if (!source.isExecutedByPlayer()) {
            source.sendError(Text.literal("此命令只能由玩家执行！").formatted(Formatting.RED));
            return 0;
        }
        
        ServerPlayerEntity player = source.getPlayerOrThrow();
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(source.getServer());
        
        // Check current role
        PlayerRoleManager.PlayerRole currentRole = roleManager.getPlayerRole(player);
        
        if (currentRole == PlayerRoleManager.PlayerRole.ESCAPER) {
            player.sendMessage(Text.literal("你已经是逃跑者了！").formatted(Formatting.YELLOW), false);
            return 0;
        }
        
        // Remove tracking compass if player was a chaser
        if (currentRole == PlayerRoleManager.PlayerRole.CHASER) {
            removeTrackingCompass(player);
        }
        
        // Set player as escaper
        roleManager.setPlayerRole(player.getUuid(), PlayerRoleManager.PlayerRole.ESCAPER);
        
        // Send confirmation message
        player.sendMessage(Text.literal("你现在是")
            .append(PlayerRoleManager.PlayerRole.ESCAPER.getColoredText())
            .append(Text.literal("！小心猎人的追踪！").formatted(Formatting.WHITE)), false);
        
        // Broadcast to chasers
        broadcastToChasers(source.getServer(), player);
        
        HunterGameMod.LOGGER.info("Player {} became an escaper", player.getName().getString());
        
        return 1;
    }
    
    private static void removeTrackingCompass(ServerPlayerEntity player) {
        // Remove tracking compass from inventory
        for (int i = 0; i < player.getInventory().size(); i++) {
            var stack = player.getInventory().getStack(i);
            if (stack.hasNbt() && stack.getNbt().getBoolean("huntergame_tracking")) {
                player.getInventory().removeStack(i);
                player.sendMessage(Text.literal("追踪指南针已被移除。").formatted(Formatting.GRAY), false);
                break;
            }
        }
    }
    
    private static void broadcastToChasers(net.minecraft.server.MinecraftServer server, ServerPlayerEntity newEscaper) {
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(server);
        Text message = Text.literal("新的目标出现！玩家 ")
            .append(Text.literal(newEscaper.getName().getString()).formatted(Formatting.AQUA))
            .append(Text.literal(" 成为了逃跑者！").formatted(Formatting.RED));
        
        // Send to all chasers
        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            if (roleManager.isChaser(player)) {
                player.sendMessage(message, false);
            }
        }
    }
}
