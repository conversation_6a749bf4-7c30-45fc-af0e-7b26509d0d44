package com.huntergame.chat;

import net.fabricmc.fabric.api.message.v1.ServerMessageEvents;
import net.minecraft.network.message.MessageType;
import net.minecraft.network.message.SignedMessage;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import com.huntergame.manager.PlayerRoleManager;
import com.huntergame.HunterGameMod;

public class ChaserChatHandler {
    private static final String CHASER_CHANNEL_PREFIX = "#";
    
    public static void initialize() {
        HunterGameMod.LOGGER.info("Initializing chaser chat handler...");
        
        // Register chat message event
        ServerMessageEvents.CHAT_MESSAGE.register((message, sender, params) -> {
            String content = message.getContent().getString();
            
            // Check if message starts with chaser channel prefix
            if (content.startsWith(CHASER_CHANNEL_PREFIX)) {
                handleChaserChannelMessage(message, sender, content.substring(1).trim());
                return false; // Cancel normal chat processing
            }
            
            return true; // Allow normal chat processing
        });
    }
    
    private static void handleChaserChannelMessage(SignedMessage message, ServerPlayerEntity sender, String content) {
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(sender.getServer());
        
        // Check if sender is a chaser
        if (!roleManager.isChaser(sender)) {
            sender.sendMessage(Text.literal("只有猎人可以在猎人频道发言！")
                .formatted(Formatting.RED), false);
            return;
        }
        
        // If content is empty, show usage
        if (content.isEmpty()) {
            sender.sendMessage(Text.literal("用法: #<消息> 在猎人频道发言")
                .formatted(Formatting.YELLOW), false);
            return;
        }
        
        // Format chaser channel message
        Text chaserMessage = formatChaserChannelMessage(sender.getName().getString(), content);
        
        // Send to all chasers
        for (ServerPlayerEntity player : sender.getServer().getPlayerManager().getPlayerList()) {
            if (roleManager.isChaser(player)) {
                player.sendMessage(chaserMessage, false);
            }
        }
        
        // Log the message
        HunterGameMod.LOGGER.info("Chaser chat [{}]: {}", sender.getName().getString(), content);
    }
    
    private static Text formatChaserChannelMessage(String playerName, String content) {
        return Text.literal("[")
            .formatted(Formatting.DARK_RED)
            .append(Text.literal("猎人频道").formatted(Formatting.RED, Formatting.BOLD))
            .append(Text.literal("] ").formatted(Formatting.DARK_RED))
            .append(Text.literal("<").formatted(Formatting.GRAY))
            .append(Text.literal(playerName).formatted(Formatting.WHITE))
            .append(Text.literal("> ").formatted(Formatting.GRAY))
            .append(Text.literal(content).formatted(Formatting.WHITE));
    }
    
    public static void sendChaserBroadcast(net.minecraft.server.MinecraftServer server, Text message) {
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(server);
        
        Text broadcastMessage = Text.literal("[")
            .formatted(Formatting.DARK_RED)
            .append(Text.literal("猎人频道").formatted(Formatting.RED, Formatting.BOLD))
            .append(Text.literal("] ").formatted(Formatting.DARK_RED))
            .append(message);
        
        // Send to all chasers
        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            if (roleManager.isChaser(player)) {
                player.sendMessage(broadcastMessage, false);
            }
        }
    }
}
