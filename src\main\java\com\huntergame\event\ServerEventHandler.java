package com.huntergame.event;

import net.fabricmc.fabric.api.event.lifecycle.v1.ServerTickEvents;
import net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import com.huntergame.manager.PlayerRoleManager;
import com.huntergame.item.TrackingCompass;
import com.huntergame.chat.ChaserChatHandler;
import com.huntergame.HunterGameMod;

public class ServerEventHandler {
    private static int tickCounter = 0;
    private static final int COMPASS_UPDATE_INTERVAL = 20; // Update every second
    
    public static void initialize() {
        HunterGameMod.LOGGER.info("Initializing server event handlers...");
        
        // Initialize chat handler
        ChaserChatHandler.initialize();
        
        // Register server tick event for compass updates
        ServerTickEvents.END_SERVER_TICK.register(ServerEventHandler::onServerTick);
        
        // Register player join/leave events
        ServerPlayConnectionEvents.JOIN.register((handler, sender, server) -> {
            onPlayerJoin(handler.getPlayer());
        });
        
        ServerPlayConnectionEvents.DISCONNECT.register((handler, server) -> {
            onPlayerLeave(handler.getPlayer());
        });
    }
    
    private static void onServerTick(MinecraftServer server) {
        tickCounter++;
        
        // Update tracking compasses every second
        if (tickCounter >= COMPASS_UPDATE_INTERVAL) {
            tickCounter = 0;
            updateAllTrackingCompasses(server);
        }
    }
    
    private static void updateAllTrackingCompasses(MinecraftServer server) {
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(server);
        
        // Update compasses for all chasers
        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            if (roleManager.isChaser(player)) {
                TrackingCompass.updateAllTrackingCompasses(player);
            }
        }
    }
    
    private static void onPlayerJoin(ServerPlayerEntity player) {
        HunterGameMod.LOGGER.info("Player {} joined the game", player.getName().getString());
        
        // Welcome message with role information
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(player.getServer());
        PlayerRoleManager.PlayerRole role = roleManager.getPlayerRole(player);
        
        if (role != PlayerRoleManager.PlayerRole.SPECTATOR) {
            player.sendMessage(net.minecraft.text.Text.literal("欢迎回到猎人游戏！你的角色是: ")
                .append(role.getColoredText()), false);
            
            if (role == PlayerRoleManager.PlayerRole.CHASER) {
                player.sendMessage(net.minecraft.text.Text.literal("使用 #<消息> 在猎人频道发言")
                    .formatted(net.minecraft.util.Formatting.YELLOW), false);
            }
        } else {
            player.sendMessage(net.minecraft.text.Text.literal("使用 /chaser 成为猎人，或 /escaper 成为逃跑者")
                .formatted(net.minecraft.util.Formatting.AQUA), false);
        }
    }
    
    private static void onPlayerLeave(ServerPlayerEntity player) {
        HunterGameMod.LOGGER.info("Player {} left the game", player.getName().getString());
    }
}
