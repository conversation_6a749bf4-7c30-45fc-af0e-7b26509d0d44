package com.huntergame.mixin;

import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import com.huntergame.manager.PlayerRoleManager;
import com.huntergame.item.TrackingCompass;
import com.huntergame.HunterGameMod;

@Mixin(ServerPlayerEntity.class)
public class PlayerRespawnMixin {
    
    @Inject(method = "moveToWorld", at = @At("RETURN"))
    private void onPlayerRespawn(ServerWorld destination, CallbackInfoReturnable<ServerPlayerEntity> cir) {
        ServerPlayerEntity player = cir.getReturnValue();
        
        if (player == null) {
            return;
        }
        
        // Check if player should get tracking compass restored
        NbtCompound persistentData = player.getPersistentData();
        if (persistentData.getBoolean("huntergame_restore_compass")) {
            restoreTrackingCompass(player);
            
            // Clear the flag
            persistentData.putBoolean("huntergame_restore_compass", false);
            
            HunterGameMod.LOGGER.info("Restored tracking compass for chaser {}", 
                player.getName().getString());
        }
    }
    
    private void restoreTrackingCompass(ServerPlayerEntity player) {
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(player.getServer());
        
        // Double check that player is still a chaser
        if (!roleManager.isChaser(player)) {
            return;
        }
        
        // Create new tracking compass
        ItemStack compass = TrackingCompass.createTrackingCompass(player);
        
        // Try to add to inventory, drop if full
        if (!player.getInventory().insertStack(compass)) {
            player.dropItem(compass, false);
            player.sendMessage(Text.literal("背包已满，追踪指南针已掉落在地上。")
                .formatted(Formatting.YELLOW), false);
        } else {
            player.sendMessage(Text.literal("追踪指南针已恢复。")
                .formatted(Formatting.GREEN), false);
        }
    }
}
