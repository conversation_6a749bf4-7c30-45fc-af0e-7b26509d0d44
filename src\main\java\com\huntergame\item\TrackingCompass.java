package com.huntergame.item;

import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.GlobalPos;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.nbt.NbtHelper;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.List;

import com.huntergame.manager.PlayerRoleManager;
import com.huntergame.HunterGameMod;

public class TrackingCompass {
    
    public static void register() {
        HunterGameMod.LOGGER.info("Registering tracking compass system...");
    }
    
    public static boolean isTrackingCompass(ItemStack stack) {
        return stack.getItem() == Items.COMPASS && 
               stack.hasNbt() && 
               stack.getNbt().getBoolean("huntergame_tracking");
    }
    
    public static void updateCompass(ServerPlayerEntity player, ItemStack compass) {
        if (!isTrackingCompass(compass)) {
            return;
        }
        
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(player.getServer());
        
        // Only update for chasers
        if (!roleManager.isChaser(player)) {
            return;
        }
        
        // Find nearest escaper
        ServerPlayerEntity nearestEscaper = findNearestEscaper(player, roleManager);
        
        if (nearestEscaper != null) {
            // Set compass to point to escaper
            setCompassTarget(compass, nearestEscaper.getBlockPos(), (ServerWorld) nearestEscaper.getWorld());
            
            // Update compass name with distance
            double distance = player.getPos().distanceTo(nearestEscaper.getPos());
            Text compassName = Text.literal("追踪指南针 ")
                .formatted(Formatting.RED, Formatting.BOLD)
                .append(Text.literal(String.format("(%.1fm)", distance))
                    .formatted(Formatting.YELLOW));
            compass.setCustomName(compassName);
        } else {
            // No escapers found, reset compass
            resetCompass(compass);
            compass.setCustomName(Text.literal("追踪指南针 (无目标)")
                .formatted(Formatting.RED, Formatting.BOLD));
        }
    }
    
    private static ServerPlayerEntity findNearestEscaper(ServerPlayerEntity chaser, PlayerRoleManager roleManager) {
        List<ServerPlayerEntity> escapers = roleManager.getEscapers(chaser.getServer());
        
        if (escapers.isEmpty()) {
            return null;
        }
        
        ServerPlayerEntity nearest = null;
        double nearestDistance = Double.MAX_VALUE;
        
        for (ServerPlayerEntity escaper : escapers) {
            // Skip if in different dimension
            if (!escaper.getWorld().equals(chaser.getWorld())) {
                continue;
            }
            
            double distance = chaser.getPos().distanceTo(escaper.getPos());
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearest = escaper;
            }
        }
        
        return nearest;
    }
    
    private static void setCompassTarget(ItemStack compass, BlockPos pos, ServerWorld world) {
        NbtCompound nbt = compass.getOrCreateNbt();
        
        // Set lodestone tracking
        nbt.putBoolean("LodestoneTracked", true);
        
        // Set lodestone position
        GlobalPos globalPos = GlobalPos.create(world.getRegistryKey(), pos);
        nbt.put("LodestonePos", NbtHelper.fromBlockPos(pos));
        nbt.putString("LodestoneDimension", world.getRegistryKey().getValue().toString());
    }
    
    private static void resetCompass(ItemStack compass) {
        NbtCompound nbt = compass.getOrCreateNbt();
        nbt.putBoolean("LodestoneTracked", false);
        nbt.remove("LodestonePos");
        nbt.remove("LodestoneDimension");
    }
    
    public static void updateAllTrackingCompasses(ServerPlayerEntity player) {
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(player.getServer());
        
        if (!roleManager.isChaser(player)) {
            return;
        }
        
        // Update compass in main hand
        ItemStack mainHand = player.getMainHandStack();
        if (isTrackingCompass(mainHand)) {
            updateCompass(player, mainHand);
        }
        
        // Update compass in off hand
        ItemStack offHand = player.getOffHandStack();
        if (isTrackingCompass(offHand)) {
            updateCompass(player, offHand);
        }
        
        // Update compasses in inventory
        for (int i = 0; i < player.getInventory().size(); i++) {
            ItemStack stack = player.getInventory().getStack(i);
            if (isTrackingCompass(stack)) {
                updateCompass(player, stack);
            }
        }
    }
    
    public static ItemStack createTrackingCompass(ServerPlayerEntity owner) {
        ItemStack compass = new ItemStack(Items.COMPASS);
        
        // Add tracking NBT
        NbtCompound nbt = compass.getOrCreateNbt();
        nbt.putBoolean("huntergame_tracking", true);
        nbt.putString("huntergame_owner", owner.getUuid().toString());
        
        // Set custom name
        compass.setCustomName(Text.literal("追踪指南针").formatted(Formatting.RED, Formatting.BOLD));
        
        // Add lore
        compass.getOrCreateSubNbt("display").putString("Lore", 
            "[{\"text\":\"指向最近的逃跑者\",\"color\":\"gray\",\"italic\":false}," +
            "{\"text\":\"死亡时不会丢失\",\"color\":\"gold\",\"italic\":false}]");
        
        return compass;
    }
}
