package com.huntergame;

import net.fabricmc.api.ModInitializer;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.huntergame.command.ChaserCommand;
import com.huntergame.command.EscaperCommand;
import com.huntergame.manager.PlayerRoleManager;
import com.huntergame.item.TrackingCompass;
import com.huntergame.event.ServerEventHandler;

public class HunterGameMod implements ModInitializer {
	public static final String MOD_ID = "huntergame";

	// This logger is used to write text to the console and the log file.
	// It is considered best practice to use your mod id as the logger's name.
	// That way, it's clear which mod wrote info, warnings, and errors.
	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	@Override
	public void onInitialize() {
		// This code runs as soon as Minecraft is in a mod-load-ready state.
		// However, some things (like resources) may still be uninitialized.
		// Proceed with mild caution.

		LOGGER.info("Hunter Game Mod initializing...");

		// Initialize player role manager
		PlayerRoleManager.initialize();

		// Register custom items
		TrackingCompass.register();

		// Initialize server event handlers
		ServerEventHandler.initialize();

		// Register commands
		CommandRegistrationCallback.EVENT.register((dispatcher, registryAccess, environment) -> {
			ChaserCommand.register(dispatcher);
			EscaperCommand.register(dispatcher);
		});

		LOGGER.info("Hunter Game Mod initialized successfully!");
	}
}