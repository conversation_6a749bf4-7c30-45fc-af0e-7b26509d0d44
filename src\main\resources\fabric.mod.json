{"schemaVersion": 1, "id": "huntergame", "version": "${version}", "name": "Hunter Game Mod", "description": "A hunter vs escaper game mod for Minecraft servers. Players can become hunters with tracking compasses or escapers trying to survive.", "authors": ["Hunter Game Developer"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/huntergame/icon.png", "environment": "*", "entrypoints": {"main": ["com.huntergame.HunterGameMod"], "client": ["com.huntergame.HunterGameModClient"]}, "mixins": ["huntergame.mixins.json", {"config": "huntergame.client.mixins.json", "environment": "client"}], "depends": {"fabricloader": ">=0.16.14", "minecraft": "~1.21.6", "java": ">=21", "fabric-api": "*"}, "suggests": {"another-mod": "*"}}