package com.huntergame;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.huntergame.client.CompassTracker;
import com.huntergame.client.ChatChannelHandler;

public class HunterGameModClient implements ClientModInitializer {
	public static final Logger LOGGER = LoggerFactory.getLogger(HunterGameMod.MOD_ID);

	@Override
	public void onInitializeClient() {
		LOGGER.info("Hunter Game Mod Client initializing...");
		
		// Initialize compass tracking system
		CompassTracker.initialize();
		
		// Initialize chat channel handler
		ChatChannelHandler.initialize();
		
		// Register client tick events for compass updates
		ClientTickEvents.END_CLIENT_TICK.register(client -> {
			if (client.player != null && client.world != null) {
				CompassTracker.updateCompass(client.player);
			}
		});

		LOGGER.info("Hunter Game Mod Client initialized successfully!");
	}
}
