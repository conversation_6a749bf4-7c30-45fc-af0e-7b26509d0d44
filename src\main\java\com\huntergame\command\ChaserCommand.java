package com.huntergame.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;

import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;

import com.huntergame.manager.PlayerRoleManager;
import com.huntergame.item.TrackingCompass;
import com.huntergame.HunterGameMod;

public class ChaserCommand {
    
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher) {
        dispatcher.register(CommandManager.literal("chaser")
            .executes(ChaserCommand::execute));
    }
    
    private static int execute(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        ServerCommandSource source = context.getSource();
        
        // Check if command is executed by a player
        if (!source.isExecutedByPlayer()) {
            source.sendError(Text.literal("此命令只能由玩家执行！").formatted(Formatting.RED));
            return 0;
        }
        
        ServerPlayerEntity player = source.getPlayerOrThrow();
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(source.getServer());
        
        // Check current role
        PlayerRoleManager.PlayerRole currentRole = roleManager.getPlayerRole(player);
        
        if (currentRole == PlayerRoleManager.PlayerRole.CHASER) {
            player.sendMessage(Text.literal("你已经是猎人了！").formatted(Formatting.YELLOW), false);
            return 0;
        }
        
        // Set player as chaser
        roleManager.setPlayerRole(player.getUuid(), PlayerRoleManager.PlayerRole.CHASER);
        
        // Give tracking compass
        giveTrackingCompass(player);
        
        // Send confirmation message
        player.sendMessage(Text.literal("你现在是")
            .append(PlayerRoleManager.PlayerRole.CHASER.getColoredText())
            .append(Text.literal("！你获得了追踪指南针。").formatted(Formatting.WHITE)), false);
        
        // Broadcast to other chasers
        broadcastToChasers(source.getServer(), player);
        
        HunterGameMod.LOGGER.info("Player {} became a chaser", player.getName().getString());
        
        return 1;
    }
    
    private static void giveTrackingCompass(ServerPlayerEntity player) {
        // Create tracking compass using the utility method
        ItemStack compass = TrackingCompass.createTrackingCompass(player);

        // Add to inventory or drop if full
        if (!player.getInventory().insertStack(compass)) {
            player.dropItem(compass, false);
            player.sendMessage(Text.literal("背包已满，追踪指南针已掉落在地上。").formatted(Formatting.YELLOW), false);
        }
    }
    
    private static void broadcastToChasers(net.minecraft.server.MinecraftServer server, ServerPlayerEntity newChaser) {
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(server);
        Text message = Text.literal("玩家 ")
            .append(Text.literal(newChaser.getName().getString()).formatted(Formatting.AQUA))
            .append(Text.literal(" 加入了猎人队伍！").formatted(Formatting.GREEN));
        
        // Send to all existing chasers
        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            if (roleManager.isChaser(player) && !player.equals(newChaser)) {
                player.sendMessage(message, false);
            }
        }
    }
}
