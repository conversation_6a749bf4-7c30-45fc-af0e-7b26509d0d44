package com.huntergame.client;

import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.item.ItemStack;

import com.huntergame.item.TrackingCompass;
import com.huntergame.HunterGameMod;

public class CompassTracker {
    private static int tickCounter = 0;
    private static final int UPDATE_INTERVAL = 20; // Update every second (20 ticks)
    
    public static void initialize() {
        HunterGameMod.LOGGER.info("Initializing client compass tracker...");
    }
    
    public static void updateCompass(ClientPlayerEntity player) {
        // Only update every second to reduce performance impact
        tickCounter++;
        if (tickCounter < UPDATE_INTERVAL) {
            return;
        }
        tickCounter = 0;
        
        // Check if player has tracking compass
        boolean hasTrackingCompass = false;
        
        // Check main hand
        ItemStack mainHand = player.getMainHandStack();
        if (TrackingCompass.isTrackingCompass(mainHand)) {
            hasTrackingCompass = true;
        }
        
        // Check off hand
        ItemStack offHand = player.getOffHandStack();
        if (TrackingCompass.isTrackingCompass(offHand)) {
            hasTrackingCompass = true;
        }
        
        // Check inventory
        if (!hasTrackingCompass) {
            for (int i = 0; i < player.getInventory().size(); i++) {
                ItemStack stack = player.getInventory().getStack(i);
                if (TrackingCompass.isTrackingCompass(stack)) {
                    hasTrackingCompass = true;
                    break;
                }
            }
        }
        
        // If player has tracking compass, request server update
        if (hasTrackingCompass) {
            // Note: In a full implementation, we would send a packet to the server
            // to request compass updates. For now, the server will handle updates
            // through its own tick events.
        }
    }
}
