package com.huntergame.mixin;

import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import net.minecraft.entity.damage.DamageSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NbtCompound;

import com.huntergame.manager.PlayerRoleManager;
import com.huntergame.item.TrackingCompass;
import com.huntergame.HunterGameMod;

@Mixin(ServerPlayerEntity.class)
public class PlayerDeathMixin {
    
    @Inject(method = "onDeath", at = @At("HEAD"))
    private void onPlayerDeath(DamageSource damageSource, CallbackInfo ci) {
        ServerPlayerEntity player = (ServerPlayerEntity) (Object) this;
        PlayerRoleManager roleManager = PlayerRoleManager.getInstance(player.getServer());
        
        // Check if player is a chaser
        if (roleManager.isChaser(player)) {
            // Store tracking compass data for restoration after respawn
            storeTrackingCompassData(player);
            
            HunterGameMod.LOGGER.info("Chaser {} died, storing tracking compass data", 
                player.getName().getString());
        }
    }
    
    private void storeTrackingCompassData(ServerPlayerEntity player) {
        // Store in player's persistent data that they should get a tracking compass on respawn
        NbtCompound persistentData = player.getPersistentData();
        persistentData.putBoolean("huntergame_restore_compass", true);
        persistentData.putString("huntergame_compass_owner", player.getUuid().toString());
    }
}
