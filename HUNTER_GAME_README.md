# 猎人游戏模组 (Hunter Game Mod)

一个为Minecraft 1.21.6服务器设计的猎人vs逃跑者游戏模组。

## 功能特性

### 角色系统
- **猎人 (Chaser)**: 使用 `/chaser` 命令成为猎人
- **逃跑者 (Escaper)**: 使用 `/escaper` 命令成为逃跑者
- **观察者 (Spectator)**: 默认角色，不参与游戏

### 追踪指南针
- 猎人获得特殊的追踪指南针
- 指南针持续指向最近的逃跑者
- 显示与目标的距离
- 死亡后不会丢失，重生时自动恢复

### 猎人频道聊天
- 猎人可以使用 `#<消息>` 在猎人频道发言
- 只有猎人能看到猎人频道的消息
- 逃跑者无法看到猎人频道的内容

### 死亡保护
- 猎人死亡后重生时保持角色状态
- 追踪指南针自动恢复到背包中

## 使用方法

### 基本命令
```
/chaser    - 成为猎人
/escaper   - 成为逃跑者
```

### 猎人频道聊天
```
#你好大家    - 在猎人频道发送消息
```

## 游戏玩法

1. **设置阶段**: 玩家使用命令选择角色
2. **游戏开始**: 猎人获得追踪指南针，开始追捕逃跑者
3. **追踪**: 指南针指向最近的逃跑者，显示距离
4. **团队协作**: 猎人可以通过专用频道协调行动
5. **持续游戏**: 死亡不会重置角色，游戏可以持续进行

## 技术特性

- 基于Fabric 1.21.6
- 服务器端和客户端同步
- 持久化数据存储
- 实时指南针更新
- 安全的聊天频道分离

## 安装说明

1. 确保服务器运行Fabric 1.21.6
2. 将模组文件放入服务器的 `mods` 文件夹
3. 重启服务器
4. 玩家可以开始使用 `/chaser` 和 `/escaper` 命令

## 注意事项

- 模组需要在服务器端安装
- 建议客户端也安装以获得最佳体验
- 追踪指南针只在同一维度内工作
- 猎人频道消息不会被记录到服务器日志中的普通聊天记录

## 开发信息

- 模组ID: `huntergame`
- 版本: 基于项目配置
- 兼容性: Minecraft 1.21.6 + Fabric
