package com.huntergame.client;

import net.fabricmc.fabric.api.client.message.v1.ClientReceiveMessageEvents;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import com.huntergame.HunterGameMod;

public class ChatChannelHandler {
    private static final String CHASER_PREFIX = "[猎人] ";
    
    public static void initialize() {
        HunterGameMod.LOGGER.info("Initializing chat channel handler...");
        
        // Register message receive event to handle chaser channel messages
        ClientReceiveMessageEvents.GAME.register((message, overlay) -> {
            // This will be handled by server-side logic
            // Client just needs to display messages properly
        });
    }
    
    public static Text formatChaserMessage(String playerName, String message) {
        return Text.literal("[")
            .formatted(Formatting.DARK_RED)
            .append(Text.literal("猎人").formatted(Formatting.RED, Formatting.BOLD))
            .append(Text.literal("] ").formatted(Formatting.DARK_RED))
            .append(Text.literal("<" + playerName + "> ").formatted(Formatting.WHITE))
            .append(Text.literal(message).formatted(Formatting.GRAY));
    }
}
